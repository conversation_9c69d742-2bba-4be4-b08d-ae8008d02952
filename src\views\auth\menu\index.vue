<template>
  <a-upload
    directory
    :customRequest="customUpload"
    accept=".txt,.jpg,.png,.pdf"
  >
    <a-button>
      <upload-outlined />
      Upload Directory
    </a-button>
  </a-upload>
</template>

<script setup>
import { UploadOutlined } from '@ant-design/icons-vue';

// MinIO 上传地址（修改为你的 MinIO 服务地址）
const uploadUrl = "https://junaili.com:9000/product/A/";

// 自定义上传逻辑，使用 PUT 而不是 POST
const customUpload = async ({ file, onSuccess, onError }) => {
  try {
    // 获取文件名，确保 MinIO 存储的路径正确
    const fileName = file.name;
    const url = `${uploadUrl}${fileName}`;

    // 使用 Fetch API 进行 PUT 上传
    const response = await fetch(url, {
      method: "PUT",
      body: file,
      headers: {
        "Content-Type": file.type, // 确保 Content-Type 正确
        "Authorization": "Bearer OoWTBcXoARiHc10hHwTB" // 如果 MinIO 需要认证
      },
    });

    if (response.ok) {
      onSuccess();
      console.log("上传成功:", fileName);
    } else {
      throw new Error(`上传失败: ${response.statusText}`);
    }
  } catch (error) {
    console.error("上传错误:", error);
    onError(error);
  }
};
</script>

<style scoped>
/* 样式 */
</style>
