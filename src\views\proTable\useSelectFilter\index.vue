<template>
  <div class="main-box">
    <div class="table-box">
      <div class="card mb10 pt0 pb0">
        <SelectFilter :data="selectFilterData" :default-values="selectFilterValues" @change="changeSelectFilter" />
      </div>
      <ProTable
        ref="proTable"
        highlight-current-row
        :columns="columns"
        :request-api="getOrderPage"
        :init-param="selectFilterValues"
      >
        <!-- 表格 header 按钮 -->
        <template #tableHeader>
          <el-button type="primary" :icon="View" @click="openStatistics">订单统计</el-button>
          <el-button type="primary" :icon="Download" plain @click="downloadFile">导出订单数据</el-button>
          <el-button type="primary" :icon="Refresh" plain @click="refreshTable">刷新数据</el-button>
        </template>
        <!-- 表格操作 -->
        <template #operation="scope">
          <el-button type="primary" link :icon="View" @click="openDrawer('查看', scope.row)">查看详情</el-button>
          <el-button type="primary" link :icon="EditPen" @click="openDrawer('编辑', scope.row)">编辑订单</el-button>
          <el-button type="primary" link :icon="Setting" @click="updateStatus(scope.row)">更新状态</el-button>
        </template>
      </ProTable>
      <OrderDrawer ref="drawerRef" />
    </div>
  </div>
</template>
<script setup name="OrderManagement">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useHandleData } from "@/hooks/useHandleData";
import { useDownload } from "@/hooks/useDownload";
import { orderStatus, payPlatform } from "@/utils/dict";
import ProTable from "@/components/ProTable/index.vue";
import SelectFilter from "@/components/SelectFilter/index.vue";
import OrderDrawer from "@/views/proTable/components/OrderDrawer.vue";
import { View, Download, Refresh, EditPen, Setting } from "@element-plus/icons-vue";
import {
  getOrderPage,
  getOrderDetail,
  updateOrder,
  updateOrderStatus,
  exportOrders,
  getOrderStatistics,
  getOrderStatusList
} from "@/api/modules/order";

// ProTable 实例
const proTable = ref();

// 表格配置项
const columns = reactive([
  { type: "selection", label: "多选", width: 80 },
  { type: "index", label: "#", width: 80 },
  { prop: "oid", label: "订单ID", width: 100, sortable: true },
  { prop: "uid", label: "用户ID", width: 100 },
  { prop: "addressee", label: "收件人", width: 120 },
  { prop: "ophone", label: "联系电话", width: 130 },
  { prop: "olocation", label: "收货地址", width: 200, showOverflowTooltip: true },
  {
    prop: "oprice",
    label: "订单金额",
    width: 120,
    sortable: true,
    render: scope => `¥${scope.row.oprice?.toFixed(2) || '0.00'}`
  },
  {
    prop: "ostatus",
    label: "订单状态",
    width: 120,
    sortable: true,
    tag: true,
    enum: orderStatus
  },
  { prop: "payPal", label: "支付平台", width: 120 },
  { prop: "payid", label: "支付单号", width: 150, showOverflowTooltip: true },
  { prop: "createTime", label: "创建时间", width: 180, sortable: true },
  { prop: "payTime", label: "支付时间", width: 180, sortable: true },
  { prop: "operation", label: "操作", width: 250, fixed: "right" }
]);

// selectFilter 数据
const selectFilterData = reactive([
  {
    title: "订单状态",
    key: "status",
    options: [
      { label: "全部", value: "" },
      { label: "待付款", value: "1", icon: "Clock" },
      { label: "待发货", value: "2", icon: "Box" },
      { label: "待收货", value: "3", icon: "Truck" },
      { label: "待评价", value: "4", icon: "Star" },
      { label: "换货", value: "5", icon: "RefreshLeft" },
      { label: "退款", value: "6", icon: "RefreshRight" }
    ]
  },
  {
    title: "支付平台",
    key: "payPlatform",
    options: [
      { label: "全部", value: "" },
      { label: "微信支付", value: "wechat", icon: "ChatDotRound" },
      { label: "支付宝", value: "alipay", icon: "Wallet" },
      { label: "银联支付", value: "unionpay", icon: "CreditCard" },
      { label: "其他", value: "other", icon: "More" }
    ]
  },
  {
    title: "日期范围",
    key: "dateRange",
    options: [
      { label: "全部", value: "" },
      { label: "今天", value: "today" },
      { label: "最近7天", value: "week" },
      { label: "最近30天", value: "month" },
      { label: "最近90天", value: "quarter" }
    ]
  }
]);

// 默认 selectFilter 参数
const selectFilterValues = ref({ status: "", payPlatform: "", dateRange: "" });
const changeSelectFilter = value => {
  ElMessage.success("筛选条件已更新");
  proTable.value.pageable.pageNum = 1;
  selectFilterValues.value = value;
};

// 刷新表格
const refreshTable = () => {
  proTable.value?.getTableList();
  ElMessage.success("数据已刷新");
};

// 更新订单状态
const statusDialogRef = ref(null);
const updateStatus = async (row) => {
  const statusOptions = orderStatus.map(item => ({ label: item.label, value: item.value }));

  ElMessageBox.prompt('请选择新的订单状态', '更新订单状态', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputType: 'select',
    inputOptions: statusOptions,
    inputValue: row.ostatus
  }).then(async ({ value }) => {
    if (value) {
      await useHandleData(
        updateOrderStatus,
        { orderId: row.oid, status: parseInt(value) },
        `更新订单【${row.oid}】状态`
      );
      proTable.value?.getTableList();
    }
  }).catch(() => {
    ElMessage.info('已取消更新');
  });
};

// 导出订单列表
const downloadFile = async () => {
  ElMessageBox.confirm("确认导出订单数据?", "温馨提示", { type: "warning" }).then(() =>
    useDownload(exportOrders, "订单列表", proTable.value?.searchParam)
  );
};

// 查看订单统计
const openStatistics = async () => {
  try {
    const { data } = await getOrderStatistics();
    ElMessage.success("统计数据获取成功，请查看控制台");
    console.log("订单统计数据:", data);
  } catch (error) {
    ElMessage.error("获取统计数据失败");
  }
};

// 打开 drawer(查看、编辑订单)
const drawerRef = ref(null);
const openDrawer = async (title, row = {}) => {
  // 如果是查看详情，需要获取完整的订单信息
  if (title === "查看") {
    try {
      const { data } = await getOrderDetail(row.oid);
      row = data;
    } catch (error) {
      ElMessage.error("获取订单详情失败");
      return;
    }
  }

  const params = {
    title,
    isView: title === "查看",
    row: { ...row },
    api: title === "编辑" ? updateOrder : undefined,
    getTableList: proTable.value?.getTableList
  };
  drawerRef.value?.acceptParams(params);
};
</script>
