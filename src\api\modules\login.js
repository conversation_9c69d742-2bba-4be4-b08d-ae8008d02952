import { PORT1 } from "@/api/config/servicePort";
import authMenuList from "@/assets/json/authMenuList.json";
import permissionLevel from "@/assets/json/permissionLevel.json";
import http from "@/api";

/**
 * @name 登录模块
 */
// 用户登录
export const loginApi = params => {
  // 临时使用模拟数据进行测试
  return Promise.resolve({
    code: 200,
    data: {
      access_token: "mock_token_" + Date.now(),
      user_info: {
        username: params.username,
        name: "测试用户"
      }
    },
    msg: "登录成功"
  });

  // 正式环境使用下面的代码
  // return http.post(PORT1 + `/login`, params, { loading: false });
};

// 获取菜单列表
export const getAuthMenuListApi = () => {
  // return http.get(PORT1 + `/menu/list`, {}, { loading: false });
  // 如果想让菜单变为本地数据，注释上一行代码，并引入本地 authMenuList.json 数据
  return authMenuList;
};

// 获取用户权限等级
export const getAuthButtonListApi = () => {
  // return http.get(PORT1 + `/auth/buttons`, {}, { loading: false });
  // 如果想让权限等级变为本地数据，注释上一行代码，并引入本地 permissionLevel.json 数据
  return permissionLevel;
};

// 用户退出登录
export const logoutApi = () => {
  return http.post(PORT1 + `/logout`);
};
