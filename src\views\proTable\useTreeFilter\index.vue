<template>
  <div class="main-box">
    <!-- 搜索区域 -->
    <div class="search-box">
      <el-card shadow="hover">
        <div class="search-content">
          <el-input
            v-model="searchText"
            placeholder="搜索产品名称..."
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
            style="width: 300px; margin-right: 16px"
          />
          <el-button type="primary" :icon="CirclePlus" @click="openDialog('add')">新增产品</el-button>
          <el-button type="success" :icon="Refresh" @click="refreshTree">刷新</el-button>
          <el-button type="warning" :icon="Download" @click="exportData">导出数据</el-button>
        </div>
      </el-card>
    </div>

    <!-- 树形数据展示区域 -->
    <div class="tree-box">
      <el-card shadow="hover">
        <template #header>
          <div class="card-header">
            <span class="title">产品分类树 ({{ treeData.length }} 个根节点)</span>
            <div class="header-actions">
              <!-- <el-button type="primary" size="small" @click="testApi">测试API</el-button> -->
              <el-button type="text" :icon="Refresh" @click="refreshTree">刷新</el-button>
              <el-button type="text" :icon="Expand" @click="expandAll">展开全部</el-button>
              <el-button type="text" :icon="Fold" @click="collapseAll">收起全部</el-button>
            </div>
          </div>
        </template>

        <!-- 数据加载状态 -->
        <div v-if="formLoading" class="loading-container">
          <el-icon class="is-loading"><Loading /></el-icon>
          <span>数据加载中...</span>
        </div>

        <!-- 空数据状态 -->
        <div v-else-if="treeData.length === 0" class="empty-container">
          <el-empty description="暂无产品数据">
            <el-button type="primary" @click="refreshTree">重新加载</el-button>
          </el-empty>
        </div>

        <!-- 树形数据 -->
        <el-tree
          v-else
          ref="treeRef"
          :data="treeData"
          :props="treeProps"
          :filter-node-method="filterNode"
          :expand-on-click-node="false"
          :default-expand-all="false"
          :highlight-current="true"
          node-key="fid"
          draggable
          @node-drop="handleNodeDrop"
          @node-click="handleNodeClick"
          class="product-tree"
        >
          <template #default="{ node, data }">
            <div class="tree-node">
              <div class="node-content">
                <span class="node-icon">
                  <el-icon v-if="data.children && data.children.length > 0">
                    <img src="./image/dead-tree.svg" alt="folder" style="width: 1em; height: 1em" />
                  </el-icon>
                  <el-icon v-else>
                    <img src="./image/yezi.svg" alt="document" style="width: 1em; height: 1em" />
                  </el-icon>
                </span>
                <span class="node-label" :title="data.fname">{{ data.fname }}</span>
                <span class="node-info">
                  <el-tag size="small" type="info">{{ data.fnumber }}</el-tag>
                  <el-tag size="small" :type="data.isLast ? 'success' : 'warning'">
                    {{ data.isLast ? "叶子节点" : "分支节点" }}
                  </el-tag>
                </span>
              </div>
              <div class="node-actions">
                <el-button type="text" size="small" :icon="Plus" @click.stop="openDialog('add', data)">添加</el-button>
                <el-button type="text" size="small" :icon="EditPen" @click.stop="openDialog('edit', data)">编辑</el-button>
                <el-button type="text" size="small" :icon="View" @click.stop="openDialog('view', data)">查看</el-button>
                <el-button type="text" size="small" :icon="Delete" @click.stop="handleDelete(data)" style="color: #f56c6c"
                  >删除</el-button
                >
              </div>
            </div>
          </template>
        </el-tree>
      </el-card>
    </div>

    <!-- 产品详情/编辑对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="600px" :close-on-click-modal="false">
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px" v-loading="formLoading">
        <el-form-item label="产品名称" prop="fname">
          <el-input v-model="formData.fname" placeholder="请输入产品名称" :disabled="dialogType === 'view'" />
        </el-form-item>
        <!-- <el-form-item label="产品编号" prop="fnumber">
          <el-input-number
            v-model="formData.fnumber"
            :min="1"
            placeholder="请输入产品编号"
            :disabled="dialogType === 'view'"
            style="width: 100%;"
          />
        </el-form-item> -->
        <!-- <el-form-item label="层级" prop="fhierarchy">
          <el-input-number
            v-model="formData.fhierarchy"
            :min="1"
            :max="10"
            placeholder="请输入层级"
            :disabled="dialogType === 'view' || dialogType === 'edit'"
            style="width: 100%;"
          />
        </el-form-item> -->
        <el-form-item label="上级产品" prop="upNumber" v-if="dialogType === 'add' && parentData">
          <el-input :value="parentData.fname" disabled placeholder="上级产品" />
        </el-form-item>
        <el-form-item label="是否叶子节点" prop="isLast" v-if="dialogType !== 'add'">
          <el-switch v-model="formData.isLast" :active-value="1" :inactive-value="0" :disabled="dialogType === 'view'" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button v-if="dialogType !== 'view'" type="primary" @click="handleSubmit" :loading="submitLoading">
            {{ dialogType === "add" ? "新增" : "保存" }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup name="useTreeFilter">
import { ref, reactive, onMounted, nextTick } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useHandleData } from "@/hooks/useHandleData";
import {
  Search,
  CirclePlus,
  Delete,
  EditPen,
  Download,
  Refresh,
  Expand,
  Fold,
  Plus,
  View,
  Folder,
  Document,
  Loading
} from "@element-plus/icons-vue";
import { getFproductList, addFproduct, editFproduct, deleteFproduct, moveFproduct, searchFproduct } from "@/api/modules/fproduct";

// 树形组件引用
const treeRef = ref();
const formRef = ref();

// 搜索相关
const searchText = ref("");

// 树形数据
const treeData = ref([]);
const treeProps = {
  children: "children",
  label: "fname"
};

// 对话框相关
const dialogVisible = ref(false);
const dialogType = ref("add"); // add, edit, view
const dialogTitle = ref("");
const formLoading = ref(false);
const submitLoading = ref(false);
const parentData = ref(null);

// 表单数据
const formData = reactive({
  fid: null,
  fname: "",
  fnumber: null,
  fhierarchy: 1,
  upNumber: 0,
  isLast: 0
});

// 表单验证规则
const formRules = {
  fname: [
    { required: true, message: "请输入产品名称", trigger: "blur" },
    { min: 2, max: 100, message: "产品名称长度在 2 到 100 个字符", trigger: "blur" }
  ],
  fnumber: [{ required: true, message: "请输入产品编号", trigger: "blur" }],
  fhierarchy: [{ required: true, message: "请选择层级", trigger: "change" }]
};

// 获取树形数据
const getTreeData = async () => {
  try {
    console.log("开始获取树形数据...");
    formLoading.value = true;

    const response = await getFproductList();
    console.log("API响应原始数据:", response);
    console.log("响应数据类型:", typeof response);
    console.log("响应是否为数组:", Array.isArray(response));

    // 处理不同的数据格式
    let processedData = [];

    // 如果response有data属性
    if (response && typeof response === "object" && "data" in response) {
      console.log("响应包含data属性:", response.data);
      console.log("data类型:", typeof response.data);
      console.log("data是否为数组:", Array.isArray(response.data));

      if (Array.isArray(response.data)) {
        processedData = response.data;
      } else if (typeof response.data === "object" && response.data !== null) {
        processedData = [response.data];
      }
    }
    // 如果response直接是数组
    else if (Array.isArray(response)) {
      console.log("响应直接是数组");
      processedData = response;
    }
    // 如果response直接是对象
    else if (typeof response === "object" && response !== null) {
      console.log("响应直接是对象");
      processedData = [response];
    }

    console.log("最终处理的数据:", processedData);
    console.log("数据长度:", processedData.length);

    treeData.value = processedData;

    if (processedData.length > 0) {
      ElMessage.success(`数据加载成功，共 ${processedData.length} 个根节点`);
    } else {
      ElMessage.warning("暂无数据");
    }
  } catch (error) {
    console.error("数据加载失败:", error);
    console.error("错误详情:", error.response || error.message);
    ElMessage.error("数据加载失败：" + (error.message || "未知错误"));
    treeData.value = [];
  } finally {
    formLoading.value = false;
  }
};

// 测试API连接
const testApi = async () => {
  try {
    console.log("=== 开始测试API ===");
    console.log("API基础URL:", import.meta.env.VITE_API_URL);
    console.log("完整请求URL:", import.meta.env.VITE_API_URL + "/f_product/all");

    // 方法1: 使用封装的API
    console.log("方法1: 使用封装的API");
    const response1 = await getFproductList();
    console.log("封装API响应:", response1);

    // 方法2: 直接使用fetch测试
    console.log("方法2: 直接使用fetch测试");
    const response2 = await fetch("/jnl_/f_product/all");
    const data2 = await response2.json();
    console.log("直接fetch响应:", data2);

    ElMessage.success("API测试完成，请查看控制台");
  } catch (error) {
    console.error("API测试失败:", error);
    ElMessage.error("API连接失败: " + error.message);
  }
};

// 刷新树形数据
const refreshTree = () => {
  getTreeData();
};

// 搜索过滤
const filterNode = (value, data) => {
  if (!value) return true;
  return data.fname.toLowerCase().includes(value.toLowerCase());
};

// 处理搜索
const handleSearch = value => {
  treeRef.value?.filter(value);
};

// 展开全部
const expandAll = () => {
  const nodes = treeRef.value?.store?.nodesMap;
  if (nodes) {
    Object.values(nodes).forEach(node => {
      node.expanded = true;
    });
  }
};

// 收起全部
const collapseAll = () => {
  const nodes = treeRef.value?.store?.nodesMap;
  if (nodes) {
    Object.values(nodes).forEach(node => {
      node.expanded = false;
    });
  }
};

// 节点点击事件
const handleNodeClick = data => {
  console.log("节点点击:", data);
};

// 节点拖拽事件
const handleNodeDrop = async (draggingNode, dropNode, dropType) => {
  try {
    const params = {
      sourceId: draggingNode.data.fid,
      targetId: dropNode.data.fid,
      dropType: dropType // 'before', 'after', 'inner'
    };

    await moveFproduct(params);
    ElMessage.success("节点移动成功");
    await getTreeData(); // 重新加载数据
  } catch (error) {
    ElMessage.error("节点移动失败：" + error.message);
    // 恢复原始状态
    await getTreeData();
  }
};

// 打开对话框
const openDialog = (type, data = null) => {
  dialogType.value = type;
  parentData.value = null;

  switch (type) {
    case "add":
      dialogTitle.value = data ? `添加子产品 - ${data.fname}` : "新增产品";
      resetForm();
      if (data) {
        parentData.value = data;
        formData.upNumber = data.fid;
        formData.fhierarchy = data.fhierarchy + 1;
      }
      break;
    case "edit":
      dialogTitle.value = `编辑产品 - ${data.fname}`;
      Object.assign(formData, data);
      break;
    case "view":
      dialogTitle.value = `查看产品 - ${data.fname}`;
      Object.assign(formData, data);
      break;
  }

  dialogVisible.value = true;
};

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    fid: null,
    fname: "",
    fnumber: null,
    fhierarchy: 1,
    upNumber: 0,
    isLast: 0
  });
  formRef.value?.clearValidate();
};
// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate();
    submitLoading.value = true;

    if (dialogType.value === "add") {
      await addFproduct(formData);
      ElMessage.success("新增成功");
    } else if (dialogType.value === "edit") {
      await editFproduct(formData);
      ElMessage.success("编辑成功");
    }

    dialogVisible.value = false;
    await getTreeData();
  } catch (error) {
    if (error.message) {
      ElMessage.error("操作失败：" + error.message);
    }
  } finally {
    submitLoading.value = false;
  }
};

// 删除节点
const handleDelete = async data => {
  try {
    await ElMessageBox.confirm(
      `确认删除产品 "${data.fname}" 吗？${
        data.children && data.children.length > 0 ? "注意：删除后其所有子产品也将被删除！" : ""
      }`,
      "删除确认",
      {
        type: "warning",
        confirmButtonText: "确认删除",
        cancelButtonText: "取消"
      }
    );

    await useHandleData(deleteFproduct, data.fid, `删除产品【${data.fname}】`);
    await getTreeData();
  } catch (error) {
    // 用户取消删除或删除失败
    if (error !== "cancel") {
      ElMessage.error("删除失败：" + error.message);
    }
  }
};

// 导出数据
const exportData = async () => {
  try {
    ElMessage.info("导出功能开发中...");
    // TODO: 实现导出功能
    // const data = await exportFproductData();
    // 下载文件逻辑
  } catch (error) {
    ElMessage.error("导出失败：" + error.message);
  }
};

// 组件挂载时获取数据
onMounted(() => {
  console.log("组件挂载，开始获取数据...");
  console.log("环境变量 VITE_API_URL:", import.meta.env.VITE_API_URL);
  getTreeData();
});
</script>
<style scoped lang="scss">
.main-box {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 16px;

  .search-box {
    .search-content {
      display: flex;
      align-items: center;
      gap: 12px;
      flex-wrap: wrap;
    }
  }

  .tree-box {
    flex: 1;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }

      .header-actions {
        display: flex;
        gap: 8px;
      }
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px;
      color: #909399;

      .el-icon {
        font-size: 24px;
        margin-bottom: 12px;
      }
    }

    .empty-container {
      padding: 20px;
    }

    .product-tree {
      max-height: calc(100vh - 300px);
      overflow-y: auto;

      .tree-node {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        padding: 4px 8px;
        border-radius: 4px;
        transition: background-color 0.3s;

        &:hover {
          background-color: #f5f7fa;

          .node-actions {
            opacity: 1;
          }
        }

        .node-content {
          display: flex;
          align-items: center;
          gap: 8px;
          flex: 1;
          min-width: 0;

          .node-icon {
            display: flex;
            align-items: center;
            color: #909399;
          }

          .node-label {
            font-size: 14px;
            color: #303133;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            flex: 1;
          }

          .node-info {
            display: flex;
            gap: 4px;
            margin-left: 8px;
          }
        }

        .node-actions {
          display: flex;
          gap: 4px;
          opacity: 0;
          transition: opacity 0.3s;

          .el-button {
            padding: 4px 8px;
            font-size: 12px;
            height: auto;
            line-height: 1;
          }
        }
      }
    }
  }
}

// 对话框样式
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 树形组件自定义样式
:deep(.el-tree) {
  .el-tree-node__content {
    height: auto;
    padding: 0;
  }

  .el-tree-node__expand-icon {
    color: #409eff;
  }

  .el-tree-node__label {
    width: 100%;
  }
}

// 卡片样式优化
:deep(.el-card) {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .el-card__header {
    padding: 16px 20px;
    border-bottom: 1px solid #ebeef5;
  }

  .el-card__body {
    padding: 20px;
  }
}

// 表单样式优化
:deep(.el-form) {
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .main-box {
    .search-box {
      .search-content {
        flex-direction: column;
        align-items: stretch;

        .el-input {
          width: 100% !important;
          margin-right: 0 !important;
          margin-bottom: 12px;
        }
      }
    }

    .tree-box {
      .product-tree {
        .tree-node {
          .node-actions {
            opacity: 1;
            flex-direction: column;
            gap: 2px;

            .el-button {
              font-size: 10px;
              padding: 2px 6px;
            }
          }
        }
      }
    }
  }
}
</style>
